# 社区论坛系统

基于SSM（Spring + SpringMVC + MyBatis）框架开发的社区论坛系统。

## 项目概述

本项目是一个功能完整的社区论坛系统，支持用户注册登录、发布帖子、评论互动、点赞收藏、分类管理、消息通知等核心功能。

## 技术栈

### 后端技术
- **框架**: Spring 5.3.21 + SpringMVC + MyBatis 3.5.10
- **数据库**: MySQL 8.0
- **连接池**: Druid 1.2.11
- **缓存**: Redis 3.8.0
- **构建工具**: Maven 3.6.0
- **服务器**: Tomcat 9.0
- **消息推送**: WebSocket

### 前端技术
- **UI框架**: Bootstrap 5.1.3
- **图标**: Font Awesome 6.0.0
- **JavaScript**: jQuery 3.6.0
- **模板引擎**: JSP + JSTL

## 功能模块

### 1. 用户管理模块
- 用户注册、登录、登出
- 用户信息管理（头像、昵称、签名等）
- 密码修改
- 用户状态管理

### 2. 帖子管理模块
- 帖子发布（支持富文本编辑）
- 帖子列表展示（分页、排序、筛选）
- 帖子详情查看
- 帖子编辑与删除
- 置顶和热门帖子

### 3. 评论管理模块
- 评论发布
- 嵌套评论（回复功能）
- 评论删除
- 评论点赞

### 4. 互动模块
- 帖子点赞/取消点赞
- 帖子收藏/取消收藏
- 评论点赞

### 5. 分类管理模块
- 分类创建与管理
- 按分类筛选帖子
- 分类统计

### 6. 消息通知模块
- 点赞、评论、收藏通知
- 系统公告通知
- 实时消息推送（WebSocket）
- 通知标记已读

## 项目结构

```
community-forum/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/community/forum/
│   │   │       ├── entity/          # 实体类
│   │   │       ├── dao/             # 数据访问层
│   │   │       ├── service/         # 业务逻辑层
│   │   │       ├── controller/      # 控制器层
│   │   │       ├── config/          # 配置类
│   │   │       ├── utils/           # 工具类
│   │   │       └── interceptor/     # 拦截器
│   │   ├── resources/
│   │   │   ├── spring/              # Spring配置文件
│   │   │   ├── mybatis/             # MyBatis配置文件
│   │   │   └── config/              # 配置文件
│   │   └── webapp/
│   │       ├── WEB-INF/
│   │       │   ├── views/           # JSP页面
│   │       │   └── web.xml          # Web配置
│   │       ├── static/              # 静态资源
│   │       └── index.jsp            # 首页
├── database/                        # 数据库脚本
├── pom.xml                         # Maven配置
└── README.md                       # 项目说明
```

## 快速开始

### 1. 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 3.0+
- Tomcat 9.0+

### 2. 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE community_forum DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行数据库脚本：
```bash
mysql -u root -p community_forum < database/community_forum.sql
```

3. 修改数据库配置文件 `src/main/resources/config/database.properties`：
```properties
jdbc.url=****************************************************************************************************************************
jdbc.username=your_username
jdbc.password=your_password
```

### 3. Redis配置
修改Redis配置文件 `src/main/resources/config/redis.properties`：
```properties
redis.host=localhost
redis.port=6379
redis.password=your_password
```

### 4. 编译和运行
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn tomcat7:run
```

访问 http://localhost:8080 即可使用系统。

## 默认账户

系统初始化后会创建以下默认分类：
- 技术讨论
- 生活分享
- 学习交流
- 问答求助
- 闲聊灌水

## API接口

### 用户相关
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `POST /api/user/logout` - 用户登出
- `GET /api/user/current` - 获取当前用户信息
- `PUT /api/user/update` - 更新用户信息
- `PUT /api/user/password` - 修改密码

### 帖子相关
- `GET /api/posts/list` - 获取帖子列表
- `GET /api/posts/detail/{id}` - 获取帖子详情
- `POST /api/posts/create` - 创建帖子
- `PUT /api/posts/update/{id}` - 更新帖子
- `DELETE /api/posts/delete/{id}` - 删除帖子

### 评论相关
- `GET /api/comments/list/{postId}` - 获取评论列表
- `POST /api/comments/create` - 创建评论
- `DELETE /api/comments/delete/{id}` - 删除评论

## 开发团队

根据项目计划，开发团队分工如下：
- **何晨**: 用户管理模块、帖子管理模块
- **翟海东**: 评论管理模块、互动模块
- **吴迪**: 分类管理模块、消息通知模块

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
