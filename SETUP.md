# 社区论坛系统 - 安装配置指南

## 环境要求

### 必需软件
- **JDK 8+** (推荐 JDK 8 或 JDK 11)
- **Maven 3.6+**
- **MySQL 8.0+**
- **Redis 3.0+**
- **Tom<PERSON> 9.0+** (可选，Maven内置)

### 开发工具
- **IntelliJ IDEA** (推荐)
- **Eclipse** (可选)

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd community-forum
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
CREATE DATABASE community_forum DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 2.2 导入数据库结构
```bash
mysql -u root -p community_forum < database/community_forum.sql
```

#### 2.3 修改数据库配置
编辑 `src/main/resources/config/database.properties`：
```properties
jdbc.url=****************************************************************************************************************************
jdbc.username=your_username
jdbc.password=your_password
```

### 3. Redis配置

#### 3.1 启动Redis服务
```bash
# Windows
redis-server.exe

# Linux/Mac
redis-server
```

#### 3.2 修改Redis配置
编辑 `src/main/resources/config/redis.properties`：
```properties
redis.host=localhost
redis.port=6379
redis.password=your_password_if_any
```

### 4. 编译和运行

#### 4.1 使用Maven编译
```bash
mvn clean compile
```

#### 4.2 运行项目
```bash
# 使用Maven内置Tomcat
mvn tomcat7:run

# 或者打包后部署到外部Tomcat
mvn clean package
# 将target/community-forum.war部署到Tomcat的webapps目录
```

#### 4.3 访问应用
打开浏览器访问：http://localhost:8080

## 项目结构说明

```
community-forum/
├── src/main/
│   ├── java/com/community/forum/
│   │   ├── entity/          # 实体类
│   │   ├── dao/             # 数据访问层
│   │   ├── service/         # 业务逻辑层
│   │   ├── controller/      # 控制器层
│   │   ├── config/          # 配置类
│   │   ├── utils/           # 工具类
│   │   └── interceptor/     # 拦截器
│   ├── resources/
│   │   ├── spring/          # Spring配置
│   │   ├── mybatis/         # MyBatis配置
│   │   ├── config/          # 数据库和Redis配置
│   │   └── logback.xml      # 日志配置
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/       # JSP页面
│       │   └── web.xml      # Web配置
│       └── index.jsp        # 首页
├── database/                # 数据库脚本
├── logs/                    # 日志文件目录
├── pom.xml                  # Maven配置
├── README.md                # 项目说明
└── SETUP.md                 # 安装指南
```

## 功能模块

### 已实现功能
- ✅ 用户注册、登录、信息管理
- ✅ 分类管理
- ✅ 基础页面框架
- ✅ WebSocket消息推送
- ✅ 登录拦截器
- ✅ 全局异常处理
- ✅ 密码加密
- ✅ 日志配置

### 待实现功能
- ⏳ 帖子管理（发布、编辑、删除）
- ⏳ 评论系统
- ⏳ 点赞和收藏功能
- ⏳ 消息通知系统
- ⏳ 文件上传功能
- ⏳ 搜索功能
- ⏳ 管理后台

## 开发指南

### 1. 添加新功能
1. 在 `entity` 包中创建实体类
2. 在 `dao` 包中创建DAO接口
3. 在 `mybatis/mapper` 中创建映射文件
4. 在 `service` 包中创建服务接口和实现
5. 在 `controller` 包中创建控制器
6. 在 `views` 目录中创建JSP页面

### 2. 数据库操作
- 使用MyBatis进行数据库操作
- 所有SQL语句写在XML映射文件中
- 使用PageHelper进行分页查询

### 3. 前端开发
- 使用Bootstrap 5作为UI框架
- 使用jQuery进行AJAX交互
- 使用Font Awesome图标库

### 4. 日志记录
- 使用SLF4J + Logback进行日志记录
- 日志文件保存在 `logs/` 目录
- 错误日志单独记录

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库配置信息正确
- 检查防火墙设置

### 2. Redis连接失败
- 检查Redis服务是否启动
- 确认Redis配置信息正确
- 检查Redis密码设置

### 3. 编译错误
- 确认JDK版本正确
- 运行 `mvn clean` 清理项目
- 检查Maven依赖是否下载完整

### 4. 页面访问404
- 确认Tomcat启动成功
- 检查URL路径是否正确
- 查看控制台日志信息

## 联系方式

如有问题，请联系开发团队：
- 何晨：用户管理、帖子管理
- 翟海东：评论管理、互动功能
- 吴迪：分类管理、消息通知

## 许可证

本项目采用 MIT 许可证。
