# 启动问题诊断指南

## 当前遇到的问题

根据错误日志，主要问题是：
1. Spring上下文启动失败 (`Error listenerStart`)
2. JDBC驱动注册问题
3. 可能的配置文件加载问题

## 解决步骤

### 1. 检查数据库连接

首先确保MySQL数据库正在运行并且可以连接：

```bash
# 检查MySQL服务状态
# Windows
net start mysql

# Linux/Mac
sudo systemctl status mysql
# 或
brew services list | grep mysql
```

### 2. 验证数据库配置

检查数据库是否存在：
```sql
SHOW DATABASES;
USE community_forum;
SHOW TABLES;
```

如果数据库不存在，创建它：
```sql
CREATE DATABASE community_forum DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

然后导入数据库结构：
```bash
mysql -u root -p community_forum < database/community_forum.sql
```

### 3. 修改数据库配置

编辑 `src/main/resources/config/database.properties`，确保配置正确：
```properties
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=*********************************************************************************************************************************************************
jdbc.username=your_username
jdbc.password=your_password
```

### 4. 使用简化配置启动

我已经创建了一个简化的Spring配置文件，暂时禁用了Redis和WebSocket功能。

要使用简化配置，修改 `web.xml` 中的Spring配置路径：

```xml
<context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:spring/applicationContext-simple.xml</param-value>
</context-param>
```

### 5. 逐步启用功能

一旦基础配置能够正常启动，再逐步启用其他功能：

1. **启用Redis**：
   - 确保Redis服务正在运行
   - 取消注释 `applicationContext.xml` 中的Redis配置

2. **启用WebSocket**：
   - 取消注释 `spring-mvc.xml` 中的WebSocket配置

### 6. 测试启动

运行测试类来验证Spring上下文是否能正常加载：
```bash
mvn test -Dtest=ApplicationStartupTest
```

### 7. 常见错误解决方案

#### 错误1: ClassNotFoundException
- 检查Maven依赖是否正确下载
- 运行 `mvn clean install` 重新下载依赖

#### 错误2: 数据库连接失败
- 检查数据库服务是否启动
- 验证用户名密码是否正确
- 确认数据库存在

#### 错误3: Redis连接失败
- 检查Redis服务是否启动
- 验证Redis配置是否正确
- 暂时禁用Redis配置

#### 错误4: 端口冲突
- 检查8080端口是否被占用
- 修改Tomcat端口配置

### 8. 调试模式启动

使用调试模式获取更详细的错误信息：
```bash
mvn tomcat7:run -X
```

### 9. 检查日志

查看详细的错误日志：
- 控制台输出
- `logs/community-forum.log`
- `logs/community-forum-error.log`

## 快速修复建议

如果仍然无法启动，请按以下顺序尝试：

1. **最小化配置**：
   ```bash
   # 使用简化的Spring配置
   cp src/main/resources/spring/applicationContext-simple.xml src/main/resources/spring/applicationContext.xml
   ```

2. **检查数据库**：
   ```bash
   mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS community_forum;"
   mysql -u root -p community_forum < database/community_forum.sql
   ```

3. **清理重建**：
   ```bash
   mvn clean install
   mvn tomcat7:run
   ```

4. **如果还是失败，请提供完整的错误日志**，包括：
   - 完整的控制台输出
   - Spring启动日志
   - 任何异常堆栈跟踪

## 联系支持

如果以上步骤都无法解决问题，请提供：
1. 完整的错误日志
2. 数据库配置信息
3. 系统环境信息（JDK版本、操作系统等）
4. Maven版本信息
