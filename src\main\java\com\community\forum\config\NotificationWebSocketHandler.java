package com.community.forum.config;

import com.community.forum.entity.User;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息通知WebSocket处理器
 */
@Component
public class NotificationWebSocketHandler implements WebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(NotificationWebSocketHandler.class);
    
    // 存储用户ID和WebSocket会话的映射
    private static final Map<Long, WebSocketSession> USER_SESSIONS = new ConcurrentHashMap<>();
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        User user = (User) session.getAttributes().get("user");
        if (user != null) {
            USER_SESSIONS.put(user.getId(), session);
            logger.info("用户 {} WebSocket连接建立成功，当前在线用户数: {}", 
                       user.getUsername(), USER_SESSIONS.size());
            
            // 发送连接成功消息
            sendMessage(session, createMessage("system", "连接成功", "WebSocket连接已建立"));
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        User user = (User) session.getAttributes().get("user");
        if (user != null) {
            logger.info("收到用户 {} 的消息: {}", user.getUsername(), message.getPayload());
            
            // 这里可以处理客户端发送的消息，比如心跳包等
            if ("ping".equals(message.getPayload().toString())) {
                sendMessage(session, createMessage("system", "pong", "心跳响应"));
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        User user = (User) session.getAttributes().get("user");
        if (user != null) {
            logger.error("用户 {} WebSocket传输错误", user.getUsername(), exception);
            USER_SESSIONS.remove(user.getId());
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        User user = (User) session.getAttributes().get("user");
        if (user != null) {
            USER_SESSIONS.remove(user.getId());
            logger.info("用户 {} WebSocket连接关闭，当前在线用户数: {}", 
                       user.getUsername(), USER_SESSIONS.size());
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 向指定用户发送消息
     */
    public static void sendMessageToUser(Long userId, String type, String title, String content) {
        WebSocketSession session = USER_SESSIONS.get(userId);
        if (session != null && session.isOpen()) {
            try {
                String message = createMessage(type, title, content);
                session.sendMessage(new TextMessage(message));
                logger.info("向用户 {} 发送消息成功: {}", userId, title);
            } catch (IOException e) {
                logger.error("向用户 {} 发送消息失败", userId, e);
                // 如果发送失败，移除无效的会话
                USER_SESSIONS.remove(userId);
            }
        }
    }

    /**
     * 向所有在线用户发送消息
     */
    public static void sendMessageToAll(String type, String title, String content) {
        String message = createMessage(type, title, content);
        USER_SESSIONS.forEach((userId, session) -> {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (IOException e) {
                    logger.error("向用户 {} 发送广播消息失败", userId, e);
                    USER_SESSIONS.remove(userId);
                }
            } else {
                USER_SESSIONS.remove(userId);
            }
        });
        logger.info("向 {} 个在线用户发送广播消息: {}", USER_SESSIONS.size(), title);
    }

    /**
     * 获取在线用户数量
     */
    public static int getOnlineUserCount() {
        return USER_SESSIONS.size();
    }

    /**
     * 检查用户是否在线
     */
    public static boolean isUserOnline(Long userId) {
        WebSocketSession session = USER_SESSIONS.get(userId);
        return session != null && session.isOpen();
    }

    /**
     * 发送消息到指定会话
     */
    private void sendMessage(WebSocketSession session, String message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        } catch (IOException e) {
            logger.error("发送消息失败", e);
        }
    }

    /**
     * 创建消息JSON字符串
     */
    private static String createMessage(String type, String title, String content) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> message = new ConcurrentHashMap<>();
            message.put("type", type);
            message.put("title", title);
            message.put("content", content);
            message.put("timestamp", System.currentTimeMillis());
            return mapper.writeValueAsString(message);
        } catch (Exception e) {
            logger.error("创建消息JSON失败", e);
            return "{\"type\":\"error\",\"title\":\"系统错误\",\"content\":\"消息格式化失败\"}";
        }
    }
}
