package com.community.forum.controller;

import com.community.forum.entity.Category;
import com.community.forum.entity.Result;
import com.community.forum.service.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@Controller
@RequestMapping("/api/categories")
public class CategoryController {

    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);

    @Autowired
    private CategoryService categoryService;

    /**
     * 获取分类列表
     */
    @GetMapping("/list")
    @ResponseBody
    public Result<List<Category>> getCategoriesList(@RequestParam(required = false) Integer status) {
        try {
            List<Category> categories = categoryService.getCategoriesWithPostCount(status);
            return Result.success(categories);
        } catch (Exception e) {
            logger.error("获取分类列表异常", e);
            return Result.error("获取分类列表失败");
        }
    }

    /**
     * 获取所有分类（不带帖子数量）
     */
    @GetMapping("/all")
    @ResponseBody
    public Result<List<Category>> getAllCategories(@RequestParam(required = false) Integer status) {
        try {
            List<Category> categories = categoryService.getAllCategories(status);
            return Result.success(categories);
        } catch (Exception e) {
            logger.error("获取所有分类异常", e);
            return Result.error("获取分类失败");
        }
    }

    /**
     * 根据ID获取分类详情
     */
    @GetMapping("/detail/{id}")
    @ResponseBody
    public Result<Category> getCategoryDetail(@PathVariable Long id) {
        try {
            Category category = categoryService.getCategoryById(id);
            if (category != null) {
                return Result.success(category);
            } else {
                return Result.notFound("分类不存在");
            }
        } catch (Exception e) {
            logger.error("获取分类详情异常", e);
            return Result.error("获取分类详情失败");
        }
    }

    /**
     * 创建分类
     */
    @PostMapping("/create")
    @ResponseBody
    public Result<String> createCategory(@RequestBody Category category) {
        try {
            // 参数验证
            if (!StringUtils.hasText(category.getName())) {
                return Result.paramError("分类名称不能为空");
            }

            if (category.getName().length() > 50) {
                return Result.paramError("分类名称长度不能超过50个字符");
            }

            boolean success = categoryService.createCategory(category);
            if (success) {
                return Result.success("创建分类成功");
            } else {
                return Result.error("创建分类失败，分类名称可能已存在");
            }
        } catch (Exception e) {
            logger.error("创建分类异常", e);
            return Result.error("创建分类失败，系统异常");
        }
    }

    /**
     * 更新分类
     */
    @PutMapping("/update/{id}")
    @ResponseBody
    public Result<String> updateCategory(@PathVariable Long id, @RequestBody Category category) {
        try {
            // 参数验证
            if (!StringUtils.hasText(category.getName())) {
                return Result.paramError("分类名称不能为空");
            }

            if (category.getName().length() > 50) {
                return Result.paramError("分类名称长度不能超过50个字符");
            }

            category.setId(id);
            boolean success = categoryService.updateCategory(category);
            if (success) {
                return Result.success("更新分类成功");
            } else {
                return Result.error("更新分类失败，分类名称可能已被使用");
            }
        } catch (Exception e) {
            logger.error("更新分类异常", e);
            return Result.error("更新分类失败，系统异常");
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/delete/{id}")
    @ResponseBody
    public Result<String> deleteCategory(@PathVariable Long id) {
        try {
            boolean success = categoryService.deleteCategory(id);
            if (success) {
                return Result.success("删除分类成功");
            } else {
                return Result.error("删除分类失败");
            }
        } catch (Exception e) {
            logger.error("删除分类异常", e);
            return Result.error("删除分类失败，系统异常");
        }
    }

    /**
     * 更新分类状态
     */
    @PutMapping("/status/{id}")
    @ResponseBody
    public Result<String> updateCategoryStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            if (status == null || (status != 0 && status != 1)) {
                return Result.paramError("状态值无效");
            }

            boolean success = categoryService.updateCategoryStatus(id, status);
            if (success) {
                return Result.success("更新分类状态成功");
            } else {
                return Result.error("更新分类状态失败");
            }
        } catch (Exception e) {
            logger.error("更新分类状态异常", e);
            return Result.error("更新分类状态失败，系统异常");
        }
    }

    /**
     * 检查分类名称是否存在
     */
    @GetMapping("/check-name")
    @ResponseBody
    public Result<Boolean> checkCategoryName(@RequestParam String name, 
                                           @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = categoryService.checkNameExists(name, excludeId);
            return Result.success(exists);
        } catch (Exception e) {
            logger.error("检查分类名称异常", e);
            return Result.error("检查失败");
        }
    }
}
