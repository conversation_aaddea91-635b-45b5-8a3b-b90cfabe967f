package com.community.forum.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 页面控制器
 */
@Controller
public class PageController {

    /**
     * 首页
     */
    @GetMapping("/")
    public String index() {
        return "forward:/index.jsp";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String login() {
        return "login";
    }

    /**
     * 注册页面
     */
    @GetMapping("/register")
    public String register() {
        return "register";
    }

    /**
     * 帖子列表页面
     */
    @GetMapping("/posts")
    public String posts() {
        return "posts";
    }

    /**
     * 帖子详情页面
     */
    @GetMapping("/post/{id}")
    public String postDetail() {
        return "post-detail";
    }

    /**
     * 发布帖子页面
     */
    @GetMapping("/post/new")
    public String newPost() {
        return "post-new";
    }

    /**
     * 编辑帖子页面
     */
    @GetMapping("/post/edit/{id}")
    public String editPost() {
        return "post-edit";
    }

    /**
     * 用户个人中心
     */
    @GetMapping("/user/profile")
    public String userProfile() {
        return "user-profile";
    }

    /**
     * 用户帖子列表
     */
    @GetMapping("/user/posts")
    public String userPosts() {
        return "user-posts";
    }

    /**
     * 用户收藏列表
     */
    @GetMapping("/user/collections")
    public String userCollections() {
        return "user-collections";
    }

    /**
     * 用户消息通知
     */
    @GetMapping("/user/notifications")
    public String userNotifications() {
        return "user-notifications";
    }

    /**
     * 分类页面
     */
    @GetMapping("/categories")
    public String categories() {
        return "categories";
    }

    /**
     * 管理后台首页
     */
    @GetMapping("/admin")
    public String admin() {
        return "admin/index";
    }

    /**
     * 管理后台 - 用户管理
     */
    @GetMapping("/admin/users")
    public String adminUsers() {
        return "admin/users";
    }

    /**
     * 管理后台 - 帖子管理
     */
    @GetMapping("/admin/posts")
    public String adminPosts() {
        return "admin/posts";
    }

    /**
     * 管理后台 - 分类管理
     */
    @GetMapping("/admin/categories")
    public String adminCategories() {
        return "admin/categories";
    }
}
