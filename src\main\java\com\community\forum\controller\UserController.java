package com.community.forum.controller;

import com.community.forum.entity.Result;
import com.community.forum.entity.User;
import com.community.forum.service.UserService;
import com.community.forum.utils.SessionUtils;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 */
@Controller
@RequestMapping("/api/user")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @ResponseBody
    public Result<String> register(@RequestBody User user) {
        try {
            // 参数验证
            if (!StringUtils.hasText(user.getUsername()) || 
                !StringUtils.hasText(user.getPassword()) ||
                !StringUtils.hasText(user.getEmail()) ||
                !StringUtils.hasText(user.getNickname())) {
                return Result.paramError("用户名、密码、邮箱和昵称不能为空");
            }

            // 用户名长度验证
            if (user.getUsername().length() < 3 || user.getUsername().length() > 20) {
                return Result.paramError("用户名长度必须在3-20个字符之间");
            }

            // 密码长度验证
            if (user.getPassword().length() < 6) {
                return Result.paramError("密码长度不能少于6个字符");
            }

            // 邮箱格式验证
            if (!user.getEmail().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
                return Result.paramError("邮箱格式不正确");
            }

            boolean success = userService.register(user);
            if (success) {
                return Result.success("注册成功");
            } else {
                return Result.error("注册失败，用户名或邮箱可能已存在");
            }
        } catch (Exception e) {
            logger.error("用户注册异常", e);
            return Result.error("注册失败，系统异常");
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @ResponseBody
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginData, 
                                            HttpServletRequest request) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");

            if (!StringUtils.hasText(username) || !StringUtils.hasText(password)) {
                return Result.paramError("用户名和密码不能为空");
            }

            User user = userService.login(username, password);
            if (user != null) {
                // 更新最后登录信息
                String clientIp = getClientIp(request);
                userService.updateLastLogin(user.getId(), clientIp);

                // 将用户信息存入Session
                HttpSession session = request.getSession();
                session.setAttribute("user", user);
                session.setMaxInactiveInterval(30 * 60); // 30分钟

                Map<String, Object> result = new HashMap<>();
                result.put("user", user);
                result.put("sessionId", session.getId());

                return Result.success("登录成功", result);
            } else {
                return Result.error("用户名或密码错误");
            }
        } catch (Exception e) {
            logger.error("用户登录异常", e);
            return Result.error("登录失败，系统异常");
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ResponseBody
    public Result<String> logout(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
            }
            return Result.success("登出成功");
        } catch (Exception e) {
            logger.error("用户登出异常", e);
            return Result.error("登出失败");
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    @ResponseBody
    public Result<User> getCurrentUser(HttpServletRequest request) {
        try {
            User user = SessionUtils.getCurrentUser(request);
            if (user != null) {
                return Result.success(user);
            } else {
                return Result.unauthorized("用户未登录");
            }
        } catch (Exception e) {
            logger.error("获取当前用户信息异常", e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/update")
    @ResponseBody
    public Result<String> updateUser(@RequestBody User user, HttpServletRequest request) {
        try {
            User currentUser = SessionUtils.getCurrentUser(request);
            if (currentUser == null) {
                return Result.unauthorized("用户未登录");
            }

            // 只允许用户更新自己的信息
            user.setId(currentUser.getId());
            user.setUsername(null); // 不允许修改用户名
            user.setPassword(null); // 不允许通过此接口修改密码

            boolean success = userService.updateUser(user);
            if (success) {
                // 更新Session中的用户信息
                User updatedUser = userService.getUserById(currentUser.getId());
                request.getSession().setAttribute("user", updatedUser);
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新用户信息异常", e);
            return Result.error("更新失败，系统异常");
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    @ResponseBody
    public Result<String> updatePassword(@RequestBody Map<String, String> passwordData, 
                                       HttpServletRequest request) {
        try {
            User currentUser = SessionUtils.getCurrentUser(request);
            if (currentUser == null) {
                return Result.unauthorized("用户未登录");
            }

            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            if (!StringUtils.hasText(oldPassword) || !StringUtils.hasText(newPassword)) {
                return Result.paramError("原密码和新密码不能为空");
            }

            if (newPassword.length() < 6) {
                return Result.paramError("新密码长度不能少于6个字符");
            }

            boolean success = userService.updatePassword(currentUser.getId(), oldPassword, newPassword);
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败，请检查原密码是否正确");
            }
        } catch (Exception e) {
            logger.error("修改密码异常", e);
            return Result.error("密码修改失败，系统异常");
        }
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @ResponseBody
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.checkUsernameExists(username, null);
            return Result.success(exists);
        } catch (Exception e) {
            logger.error("检查用户名异常", e);
            return Result.error("检查失败");
        }
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    @ResponseBody
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.checkEmailExists(email, null);
            return Result.success(exists);
        } catch (Exception e) {
            logger.error("检查邮箱异常", e);
            return Result.error("检查失败");
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
