package com.community.forum.dao;

import com.community.forum.entity.Category;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分类数据访问接口
 */
public interface CategoryDao {

    /**
     * 根据ID查询分类
     */
    Category selectById(@Param("id") Long id);

    /**
     * 插入分类
     */
    int insert(Category category);

    /**
     * 更新分类
     */
    int update(Category category);

    /**
     * 更新分类状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 删除分类
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询所有分类（按排序）
     */
    List<Category> selectAll(@Param("status") Integer status);

    /**
     * 查询分类列表（带帖子数量）
     */
    List<Category> selectListWithPostCount(@Param("status") Integer status);

    /**
     * 检查分类名称是否存在
     */
    int checkNameExists(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 获取最大排序值
     */
    Integer getMaxSortOrder();

}
