package com.community.forum.dao;

import com.community.forum.entity.Collection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收藏数据访问接口
 */
public interface CollectionDao {

    /**
     * 根据ID查询收藏记录
     */
    Collection selectById(@Param("id") Long id);

    /**
     * 插入收藏记录
     */
    int insert(Collection collection);

    /**
     * 删除收藏记录
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户和帖子删除收藏记录
     */
    int deleteByUserAndPost(@Param("userId") Long userId, @Param("postId") Long postId);

    /**
     * 查询用户对帖子的收藏记录
     */
    Collection selectByUserAndPost(@Param("userId") Long userId, @Param("postId") Long postId);

    /**
     * 查询用户的收藏列表
     */
    List<Collection> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询帖子的收藏列表
     */
    List<Collection> selectByPostId(@Param("postId") Long postId);

    /**
     * 统计帖子的收藏数量
     */
    int countByPostId(@Param("postId") Long postId);

    /**
     * 统计用户的收藏数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否收藏了帖子
     */
    boolean checkUserCollected(@Param("userId") Long userId, @Param("postId") Long postId);

    /**
     * 批量检查用户收藏状态
     */
    List<Long> selectCollectedPostIds(@Param("userId") Long userId, @Param("postIds") List<Long> postIds);

}
