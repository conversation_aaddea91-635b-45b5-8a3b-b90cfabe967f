package com.community.forum.dao;

import com.community.forum.entity.Comment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评论数据访问接口
 */
public interface CommentDao {

    /**
     * 根据ID查询评论
     */
    Comment selectById(@Param("id") Long id);

    /**
     * 插入评论
     */
    int insert(Comment comment);

    /**
     * 更新评论
     */
    int update(Comment comment);

    /**
     * 更新评论状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 删除评论
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据帖子ID查询评论列表（一级评论）
     */
    List<Comment> selectByPostId(@Param("postId") Long postId, @Param("status") Integer status);

    /**
     * 根据父评论ID查询子评论列表
     */
    List<Comment> selectByParentId(@Param("parentId") Long parentId, @Param("status") Integer status);

    /**
     * 查询用户的评论列表
     */
    List<Comment> selectByUserId(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 增加点赞数
     */
    int increaseLikeCount(@Param("id") Long id);

    /**
     * 减少点赞数
     */
    int decreaseLikeCount(@Param("id") Long id);

    /**
     * 统计帖子的评论数量
     */
    int countByPostId(@Param("postId") Long postId, @Param("status") Integer status);

    /**
     * 统计用户的评论数量
     */
    int countByUserId(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 删除帖子的所有评论
     */
    int deleteByPostId(@Param("postId") Long postId);

}
