package com.community.forum.dao;

import com.community.forum.entity.Like;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点赞数据访问接口
 */
public interface LikeDao {

    /**
     * 根据ID查询点赞记录
     */
    Like selectById(@Param("id") Long id);

    /**
     * 插入点赞记录
     */
    int insert(Like like);

    /**
     * 删除点赞记录
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户和目标删除点赞记录
     */
    int deleteByUserAndTarget(@Param("userId") Long userId, 
                             @Param("targetId") Long targetId, 
                             @Param("targetType") Integer targetType);

    /**
     * 查询用户对目标的点赞记录
     */
    Like selectByUserAndTarget(@Param("userId") Long userId, 
                              @Param("targetId") Long targetId, 
                              @Param("targetType") Integer targetType);

    /**
     * 查询用户的点赞列表
     */
    List<Like> selectByUserId(@Param("userId") Long userId, @Param("targetType") Integer targetType);

    /**
     * 查询目标的点赞列表
     */
    List<Like> selectByTarget(@Param("targetId") Long targetId, @Param("targetType") Integer targetType);

    /**
     * 统计目标的点赞数量
     */
    int countByTarget(@Param("targetId") Long targetId, @Param("targetType") Integer targetType);

    /**
     * 检查用户是否点赞了目标
     */
    boolean checkUserLiked(@Param("userId") Long userId, 
                          @Param("targetId") Long targetId, 
                          @Param("targetType") Integer targetType);

    /**
     * 批量检查用户点赞状态
     */
    List<Long> selectLikedTargetIds(@Param("userId") Long userId, 
                                   @Param("targetIds") List<Long> targetIds, 
                                   @Param("targetType") Integer targetType);

}
