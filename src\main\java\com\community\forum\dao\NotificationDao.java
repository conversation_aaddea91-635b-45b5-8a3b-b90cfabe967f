package com.community.forum.dao;

import com.community.forum.entity.Notification;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息通知数据访问接口
 */
public interface NotificationDao {

    /**
     * 根据ID查询通知
     */
    Notification selectById(@Param("id") Long id);

    /**
     * 插入通知
     */
    int insert(Notification notification);

    /**
     * 更新通知
     */
    int update(Notification notification);

    /**
     * 删除通知
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询用户的通知列表
     */
    List<Notification> selectByUserId(@Param("userId") Long userId, 
                                     @Param("type") Integer type, 
                                     @Param("isRead") Integer isRead);

    /**
     * 标记通知为已读
     */
    int markAsRead(@Param("id") Long id);

    /**
     * 批量标记通知为已读
     */
    int batchMarkAsRead(@Param("ids") List<Long> ids);

    /**
     * 标记用户所有通知为已读
     */
    int markAllAsReadByUserId(@Param("userId") Long userId);

    /**
     * 统计用户未读通知数量
     */
    int countUnreadByUserId(@Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 删除用户的通知
     */
    int deleteByUserId(@Param("userId") Long userId, @Param("type") Integer type);

    /**
     * 批量删除通知
     */
    int batchDelete(@Param("ids") List<Long> ids);

}
