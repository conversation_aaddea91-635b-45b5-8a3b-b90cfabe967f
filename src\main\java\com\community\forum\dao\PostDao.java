package com.community.forum.dao;

import com.community.forum.entity.Post;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 帖子数据访问接口
 */
public interface PostDao {

    /**
     * 根据ID查询帖子
     */
    Post selectById(@Param("id") Long id);

    /**
     * 根据ID查询帖子详情（包含用户和分类信息）
     */
    Post selectDetailById(@Param("id") Long id);

    /**
     * 插入帖子
     */
    int insert(Post post);

    /**
     * 更新帖子
     */
    int update(Post post);

    /**
     * 更新帖子状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 删除帖子
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询帖子列表
     */
    List<Post> selectList(@Param("categoryId") Long categoryId, 
                         @Param("keyword") String keyword, 
                         @Param("status") Integer status,
                         @Param("orderBy") String orderBy);

    /**
     * 查询用户的帖子列表
     */
    List<Post> selectByUserId(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查询热门帖子
     */
    List<Post> selectHotPosts(@Param("limit") Integer limit);

    /**
     * 查询置顶帖子
     */
    List<Post> selectTopPosts();

    /**
     * 增加浏览次数
     */
    int increaseViewCount(@Param("id") Long id);

    /**
     * 增加点赞数
     */
    int increaseLikeCount(@Param("id") Long id);

    /**
     * 减少点赞数
     */
    int decreaseLikeCount(@Param("id") Long id);

    /**
     * 增加评论数
     */
    int increaseCommentCount(@Param("id") Long id);

    /**
     * 减少评论数
     */
    int decreaseCommentCount(@Param("id") Long id);

    /**
     * 增加收藏数
     */
    int increaseCollectCount(@Param("id") Long id);

    /**
     * 减少收藏数
     */
    int decreaseCollectCount(@Param("id") Long id);

    /**
     * 设置置顶状态
     */
    int updateTopStatus(@Param("id") Long id, @Param("isTop") Integer isTop);

    /**
     * 设置热门状态
     */
    int updateHotStatus(@Param("id") Long id, @Param("isHot") Integer isHot);

    /**
     * 统计帖子数量
     */
    int countPosts(@Param("categoryId") Long categoryId, 
                   @Param("keyword") String keyword, 
                   @Param("status") Integer status);

}
