package com.community.forum.dao;

import com.community.forum.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户数据访问接口
 */
public interface UserDao {

    /**
     * 根据ID查询用户
     */
    User selectById(@Param("id") Long id);

    /**
     * 根据用户名查询用户
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 插入用户
     */
    int insert(User user);

    /**
     * 更新用户信息
     */
    int update(User user);

    /**
     * 更新用户状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新最后登录信息
     */
    int updateLastLogin(@Param("id") Long id, @Param("lastLoginIp") String lastLoginIp);

    /**
     * 删除用户
     */
    int deleteById(@Param("id") Long id);

    /**
     * 查询用户列表
     */
    List<User> selectList(@Param("keyword") String keyword, @Param("status") Integer status);

    /**
     * 统计用户数量
     */
    int countUsers(@Param("keyword") String keyword, @Param("status") Integer status);

    /**
     * 检查用户名是否存在
     */
    int checkUsernameExists(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     */
    int checkEmailExists(@Param("email") String email, @Param("excludeId") Long excludeId);

}
