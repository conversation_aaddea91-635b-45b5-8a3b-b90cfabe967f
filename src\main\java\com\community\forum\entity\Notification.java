package com.community.forum.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知实体类
 */
public class Notification implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long userId; // 接收用户ID
    private Long senderId; // 发送者ID
    private Integer type; // 通知类型：1-点赞，2-评论，3-收藏，4-系统通知
    private String title;
    private String content;
    private Long targetId; // 关联目标ID
    private Integer targetType; // 关联目标类型：1-帖子，2-评论
    private Integer isRead; // 是否已读：0-未读，1-已读
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    // 关联对象
    private User user;
    private User sender;

    // 构造方法
    public Notification() {}

    public Notification(Long userId, Long senderId, Integer type, String title, String content, Long targetId, Integer targetType) {
        this.userId = userId;
        this.senderId = senderId;
        this.type = type;
        this.title = title;
        this.content = content;
        this.targetId = targetId;
        this.targetType = targetType;
        this.isRead = 0;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public User getSender() {
        return sender;
    }

    public void setSender(User sender) {
        this.sender = sender;
    }

    @Override
    public String toString() {
        return "Notification{" +
                "id=" + id +
                ", userId=" + userId +
                ", senderId=" + senderId +
                ", type=" + type +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", targetId=" + targetId +
                ", targetType=" + targetType +
                ", isRead=" + isRead +
                ", createdTime=" + createdTime +
                '}';
    }
}
