package com.community.forum.entity;

import java.io.Serializable;

/**
 * 统一响应结果类
 */
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer code;
    private String message;
    private T data;

    // 成功状态码
    public static final int SUCCESS_CODE = 200;
    // 失败状态码
    public static final int ERROR_CODE = 500;
    // 参数错误状态码
    public static final int PARAM_ERROR_CODE = 400;
    // 未授权状态码
    public static final int UNAUTHORIZED_CODE = 401;
    // 禁止访问状态码
    public static final int FORBIDDEN_CODE = 403;
    // 资源不存在状态码
    public static final int NOT_FOUND_CODE = 404;

    // 构造方法
    public Result() {}

    public Result(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // 成功响应
    public static <T> Result<T> success() {
        return new Result<>(SUCCESS_CODE, "操作成功");
    }

    public static <T> Result<T> success(String message) {
        return new Result<>(SUCCESS_CODE, message);
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(SUCCESS_CODE, "操作成功", data);
    }

    public static <T> Result<T> success(String message, T data) {
        return new Result<>(SUCCESS_CODE, message, data);
    }

    // 失败响应
    public static <T> Result<T> error() {
        return new Result<>(ERROR_CODE, "操作失败");
    }

    public static <T> Result<T> error(String message) {
        return new Result<>(ERROR_CODE, message);
    }

    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message);
    }

    // 参数错误响应
    public static <T> Result<T> paramError(String message) {
        return new Result<>(PARAM_ERROR_CODE, message);
    }

    // 未授权响应
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(UNAUTHORIZED_CODE, message);
    }

    // 禁止访问响应
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(FORBIDDEN_CODE, message);
    }

    // 资源不存在响应
    public static <T> Result<T> notFound(String message) {
        return new Result<>(NOT_FOUND_CODE, message);
    }

    // 判断是否成功
    public boolean isSuccess() {
        return this.code != null && this.code.equals(SUCCESS_CODE);
    }

    // Getter和Setter方法
    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
