package com.community.forum.interceptor;

import com.community.forum.entity.Result;
import com.community.forum.utils.SessionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 登录拦截器
 */
public class LoginInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(LoginInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 检查用户是否已登录
        if (!SessionUtils.isLoggedIn(request)) {
            logger.warn("用户未登录，拦截请求: {}", request.getRequestURI());
            
            // 判断是否为AJAX请求
            String requestedWith = request.getHeader("X-Requested-With");
            if ("XMLHttpRequest".equals(requestedWith) || request.getRequestURI().startsWith("/api/")) {
                // AJAX请求，返回JSON格式的错误信息
                writeJsonResponse(response, Result.unauthorized("用户未登录，请先登录"));
            } else {
                // 普通请求，重定向到登录页面
                response.sendRedirect(request.getContextPath() + "/login");
            }
            return false;
        }
        
        return true;
    }

    /**
     * 写入JSON响应
     */
    private void writeJsonResponse(HttpServletResponse response, Result<?> result) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonResult = objectMapper.writeValueAsString(result);
        
        PrintWriter writer = response.getWriter();
        writer.write(jsonResult);
        writer.flush();
        writer.close();
    }
}
