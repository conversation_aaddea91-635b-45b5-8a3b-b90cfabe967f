package com.community.forum.service;

import com.community.forum.entity.Category;

import java.util.List;

/**
 * 分类服务接口
 */
public interface CategoryService {

    /**
     * 根据ID查询分类
     */
    Category getCategoryById(Long id);

    /**
     * 创建分类
     */
    boolean createCategory(Category category);

    /**
     * 更新分类
     */
    boolean updateCategory(Category category);

    /**
     * 删除分类
     */
    boolean deleteCategory(Long id);

    /**
     * 获取所有分类
     */
    List<Category> getAllCategories(Integer status);

    /**
     * 获取分类列表（带帖子数量）
     */
    List<Category> getCategoriesWithPostCount(Integer status);

    /**
     * 检查分类名称是否存在
     */
    boolean checkNameExists(String name, Long excludeId);

    /**
     * 更新分类状态
     */
    boolean updateCategoryStatus(Long id, Integer status);

}
