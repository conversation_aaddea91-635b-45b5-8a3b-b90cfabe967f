package com.community.forum.service;

import com.community.forum.entity.User;
import com.github.pagehelper.PageInfo;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户注册
     */
    boolean register(User user);

    /**
     * 用户登录
     */
    User login(String username, String password);

    /**
     * 根据ID查询用户
     */
    User getUserById(Long id);

    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);

    /**
     * 更新用户信息
     */
    boolean updateUser(User user);

    /**
     * 更新用户密码
     */
    boolean updatePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 更新用户状态
     */
    boolean updateUserStatus(Long id, Integer status);

    /**
     * 更新最后登录信息
     */
    boolean updateLastLogin(Long id, String lastLoginIp);

    /**
     * 删除用户
     */
    boolean deleteUser(Long id);

    /**
     * 分页查询用户列表
     */
    PageInfo<User> getUserList(int pageNum, int pageSize, String keyword, Integer status);

    /**
     * 检查用户名是否存在
     */
    boolean checkUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     */
    boolean checkEmailExists(String email, Long excludeId);

    /**
     * 验证密码
     */
    boolean validatePassword(String rawPassword, String encodedPassword);

    /**
     * 加密密码
     */
    String encodePassword(String password);

}
