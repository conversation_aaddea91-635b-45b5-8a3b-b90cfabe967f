package com.community.forum.service.impl;

import com.community.forum.dao.CategoryDao;
import com.community.forum.entity.Category;
import com.community.forum.service.CategoryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 分类服务实现类
 */
@Service
@Transactional
public class CategoryServiceImpl implements CategoryService {

    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    @Autowired
    private CategoryDao categoryDao;

    @Override
    @Transactional(readOnly = true)
    public Category getCategoryById(Long id) {
        try {
            return categoryDao.selectById(id);
        } catch (Exception e) {
            logger.error("查询分类失败: {}", id, e);
            return null;
        }
    }

    @Override
    public boolean createCategory(Category category) {
        try {
            // 检查分类名称是否存在
            if (checkNameExists(category.getName(), null)) {
                logger.warn("分类名称已存在: {}", category.getName());
                return false;
            }

            // 设置排序值
            if (category.getSortOrder() == null) {
                Integer maxSortOrder = categoryDao.getMaxSortOrder();
                category.setSortOrder(maxSortOrder + 1);
            }

            // 设置默认状态
            if (category.getStatus() == null) {
                category.setStatus(1);
            }

            int result = categoryDao.insert(category);
            logger.info("创建分类成功: {}", category.getName());
            return result > 0;
        } catch (Exception e) {
            logger.error("创建分类失败", e);
            return false;
        }
    }

    @Override
    public boolean updateCategory(Category category) {
        try {
            // 检查分类名称是否被其他分类使用
            if (checkNameExists(category.getName(), category.getId())) {
                logger.warn("分类名称已被其他分类使用: {}", category.getName());
                return false;
            }

            int result = categoryDao.update(category);
            logger.info("更新分类成功: {}", category.getId());
            return result > 0;
        } catch (Exception e) {
            logger.error("更新分类失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteCategory(Long id) {
        try {
            // 这里应该检查是否有帖子使用了这个分类
            // 如果有，应该禁止删除或者提供转移功能
            
            int result = categoryDao.deleteById(id);
            logger.info("删除分类成功: {}", id);
            return result > 0;
        } catch (Exception e) {
            logger.error("删除分类失败", e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Category> getAllCategories(Integer status) {
        try {
            return categoryDao.selectAll(status);
        } catch (Exception e) {
            logger.error("查询分类列表失败", e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Category> getCategoriesWithPostCount(Integer status) {
        try {
            return categoryDao.selectListWithPostCount(status);
        } catch (Exception e) {
            logger.error("查询分类列表（带帖子数量）失败", e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean checkNameExists(String name, Long excludeId) {
        try {
            int count = categoryDao.checkNameExists(name, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查分类名称是否存在失败", e);
            return false;
        }
    }

    @Override
    public boolean updateCategoryStatus(Long id, Integer status) {
        try {
            int result = categoryDao.updateStatus(id, status);
            logger.info("更新分类状态成功: {} -> {}", id, status);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新分类状态失败", e);
            return false;
        }
    }
}
