package com.community.forum.service.impl;

import com.community.forum.dao.UserDao;
import com.community.forum.entity.User;
import com.community.forum.service.UserService;
import com.community.forum.utils.PasswordUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserDao userDao;

    @Override
    public boolean register(User user) {
        try {
            // 检查用户名是否存在
            if (checkUsernameExists(user.getUsername(), null)) {
                logger.warn("用户名已存在: {}", user.getUsername());
                return false;
            }

            // 检查邮箱是否存在
            if (checkEmailExists(user.getEmail(), null)) {
                logger.warn("邮箱已存在: {}", user.getEmail());
                return false;
            }

            // 加密密码
            user.setPassword(encodePassword(user.getPassword()));

            // 设置默认值
            if (user.getAvatar() == null) {
                user.setAvatar("/images/default-avatar.png");
            }
            if (user.getGender() == null) {
                user.setGender(0);
            }
            if (user.getStatus() == null) {
                user.setStatus(1);
            }

            int result = userDao.insert(user);
            logger.info("用户注册成功: {}", user.getUsername());
            return result > 0;
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public User login(String username, String password) {
        try {
            User user = userDao.selectByUsername(username);
            if (user == null) {
                logger.warn("用户不存在: {}", username);
                return null;
            }

            if (user.getStatus() != 1) {
                logger.warn("用户已被禁用: {}", username);
                return null;
            }

            if (!validatePassword(password, user.getPassword())) {
                logger.warn("密码错误: {}", username);
                return null;
            }

            logger.info("用户登录成功: {}", username);
            return user;
        } catch (Exception e) {
            logger.error("用户登录失败", e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserById(Long id) {
        try {
            return userDao.selectById(id);
        } catch (Exception e) {
            logger.error("查询用户失败: {}", id, e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserByUsername(String username) {
        try {
            return userDao.selectByUsername(username);
        } catch (Exception e) {
            logger.error("查询用户失败: {}", username, e);
            return null;
        }
    }

    @Override
    public boolean updateUser(User user) {
        try {
            // 检查邮箱是否被其他用户使用
            if (StringUtils.hasText(user.getEmail()) && 
                checkEmailExists(user.getEmail(), user.getId())) {
                logger.warn("邮箱已被其他用户使用: {}", user.getEmail());
                return false;
            }

            int result = userDao.update(user);
            logger.info("更新用户信息成功: {}", user.getId());
            return result > 0;
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return false;
        }
    }

    @Override
    public boolean updatePassword(Long userId, String oldPassword, String newPassword) {
        try {
            User user = userDao.selectById(userId);
            if (user == null) {
                logger.warn("用户不存在: {}", userId);
                return false;
            }

            if (!validatePassword(oldPassword, user.getPassword())) {
                logger.warn("原密码错误: {}", userId);
                return false;
            }

            user.setPassword(encodePassword(newPassword));
            int result = userDao.update(user);
            logger.info("更新密码成功: {}", userId);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新密码失败", e);
            return false;
        }
    }

    @Override
    public boolean updateUserStatus(Long id, Integer status) {
        try {
            int result = userDao.updateStatus(id, status);
            logger.info("更新用户状态成功: {} -> {}", id, status);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return false;
        }
    }

    @Override
    public boolean updateLastLogin(Long id, String lastLoginIp) {
        try {
            int result = userDao.updateLastLogin(id, lastLoginIp);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新最后登录信息失败", e);
            return false;
        }
    }

    @Override
    public boolean deleteUser(Long id) {
        try {
            int result = userDao.deleteById(id);
            logger.info("删除用户成功: {}", id);
            return result > 0;
        } catch (Exception e) {
            logger.error("删除用户失败", e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageInfo<User> getUserList(int pageNum, int pageSize, String keyword, Integer status) {
        try {
            PageHelper.startPage(pageNum, pageSize);
            List<User> users = userDao.selectList(keyword, status);
            return new PageInfo<>(users);
        } catch (Exception e) {
            logger.error("查询用户列表失败", e);
            return new PageInfo<>();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean checkUsernameExists(String username, Long excludeId) {
        try {
            int count = userDao.checkUsernameExists(username, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查用户名是否存在失败", e);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean checkEmailExists(String email, Long excludeId) {
        try {
            int count = userDao.checkEmailExists(email, excludeId);
            return count > 0;
        } catch (Exception e) {
            logger.error("检查邮箱是否存在失败", e);
            return false;
        }
    }

    @Override
    public boolean validatePassword(String rawPassword, String encodedPassword) {
        return PasswordUtils.matches(rawPassword, encodedPassword);
    }

    @Override
    public String encodePassword(String password) {
        return PasswordUtils.encode(password);
    }
}
