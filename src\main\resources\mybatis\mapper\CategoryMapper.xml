<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.community.forum.dao.CategoryDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="Category">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="icon" property="icon"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 带帖子数量的结果映射 -->
    <resultMap id="CategoryWithPostCountMap" type="Category" extends="BaseResultMap">
        <result column="post_count" property="postCount"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, description, icon, sort_order, status, created_time, updated_time
    </sql>

    <!-- 根据ID查询分类 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM categories
        WHERE id = #{id}
    </select>

    <!-- 插入分类 -->
    <insert id="insert" parameterType="Category" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO categories (name, description, icon, sort_order, status)
        VALUES (#{name}, #{description}, #{icon}, #{sortOrder}, #{status})
    </insert>

    <!-- 更新分类 -->
    <update id="update" parameterType="Category">
        UPDATE categories
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            updated_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新分类状态 -->
    <update id="updateStatus">
        UPDATE categories
        SET status = #{status}, updated_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 删除分类 -->
    <delete id="deleteById">
        DELETE FROM categories WHERE id = #{id}
    </delete>

    <!-- 查询所有分类（按排序） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM categories
        <where>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY sort_order ASC, created_time ASC
    </select>

    <!-- 查询分类列表（带帖子数量） -->
    <select id="selectListWithPostCount" resultMap="CategoryWithPostCountMap">
        SELECT c.*, COALESCE(p.post_count, 0) as post_count
        FROM categories c
        LEFT JOIN (
            SELECT category_id, COUNT(*) as post_count
            FROM posts
            WHERE status = 1
            GROUP BY category_id
        ) p ON c.id = p.category_id
        <where>
            <if test="status != null">
                AND c.status = #{status}
            </if>
        </where>
        ORDER BY c.sort_order ASC, c.created_time ASC
    </select>

    <!-- 检查分类名称是否存在 -->
    <select id="checkNameExists" resultType="int">
        SELECT COUNT(*)
        FROM categories
        WHERE name = #{name}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取最大排序值 -->
    <select id="getMaxSortOrder" resultType="int">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM categories
    </select>

</mapper>
