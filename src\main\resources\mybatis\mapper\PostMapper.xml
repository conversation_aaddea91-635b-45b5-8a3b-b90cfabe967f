<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.community.forum.dao.PostDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="Post">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="summary" property="summary"/>
        <result column="cover_image" property="coverImage"/>
        <result column="user_id" property="userId"/>
        <result column="category_id" property="categoryId"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="collect_count" property="collectCount"/>
        <result column="is_top" property="isTop"/>
        <result column="is_hot" property="isHot"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 详情结果映射（包含用户和分类信息） -->
    <resultMap id="DetailResultMap" type="Post" extends="BaseResultMap">
        <association property="user" javaType="User">
            <id column="user_id" property="id"/>
            <result column="username" property="username"/>
            <result column="nickname" property="nickname"/>
            <result column="avatar" property="avatar"/>
            <result column="signature" property="signature"/>
        </association>
        <association property="category" javaType="Category">
            <id column="category_id" property="id"/>
            <result column="category_name" property="name"/>
            <result column="category_icon" property="icon"/>
        </association>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, title, content, summary, cover_image, user_id, category_id, 
        view_count, like_count, comment_count, collect_count, 
        is_top, is_hot, status, created_time, updated_time
    </sql>

    <!-- 详情查询字段 -->
    <sql id="Detail_Column_List">
        p.id, p.title, p.content, p.summary, p.cover_image, p.user_id, p.category_id,
        p.view_count, p.like_count, p.comment_count, p.collect_count,
        p.is_top, p.is_hot, p.status, p.created_time, p.updated_time,
        u.username, u.nickname, u.avatar, u.signature,
        c.name as category_name, c.icon as category_icon
    </sql>

    <!-- 根据ID查询帖子 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        WHERE id = #{id}
    </select>

    <!-- 根据ID查询帖子详情（包含用户和分类信息） -->
    <select id="selectDetailById" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.id = #{id}
    </select>

    <!-- 插入帖子 -->
    <insert id="insert" parameterType="Post" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO posts (title, content, summary, cover_image, user_id, category_id, 
                          view_count, like_count, comment_count, collect_count, 
                          is_top, is_hot, status)
        VALUES (#{title}, #{content}, #{summary}, #{coverImage}, #{userId}, #{categoryId},
                #{viewCount}, #{likeCount}, #{commentCount}, #{collectCount},
                #{isTop}, #{isHot}, #{status})
    </insert>

    <!-- 更新帖子 -->
    <update id="update" parameterType="Post">
        UPDATE posts
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="status != null">status = #{status},</if>
            updated_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新帖子状态 -->
    <update id="updateStatus">
        UPDATE posts
        SET status = #{status}, updated_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 删除帖子 -->
    <delete id="deleteById">
        DELETE FROM posts WHERE id = #{id}
    </delete>

    <!-- 查询帖子列表 -->
    <select id="selectList" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        <where>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (p.title LIKE CONCAT('%', #{keyword}, '%') 
                OR p.content LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND p.status = #{status}
            </if>
        </where>
        <choose>
            <when test="orderBy == 'hot'">
                ORDER BY p.is_hot DESC, p.like_count DESC, p.view_count DESC, p.created_time DESC
            </when>
            <when test="orderBy == 'view'">
                ORDER BY p.view_count DESC, p.created_time DESC
            </when>
            <when test="orderBy == 'like'">
                ORDER BY p.like_count DESC, p.created_time DESC
            </when>
            <otherwise>
                ORDER BY p.is_top DESC, p.created_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 查询用户的帖子列表 -->
    <select id="selectByUserId" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.user_id = #{userId}
        <if test="status != null">
            AND p.status = #{status}
        </if>
        ORDER BY p.created_time DESC
    </select>

    <!-- 查询热门帖子 -->
    <select id="selectHotPosts" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 1
        ORDER BY p.like_count DESC, p.view_count DESC, p.comment_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询置顶帖子 -->
    <select id="selectTopPosts" resultMap="DetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.is_top = 1 AND p.status = 1
        ORDER BY p.created_time DESC
    </select>

    <!-- 增加浏览次数 -->
    <update id="increaseViewCount">
        UPDATE posts SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 增加点赞数 -->
    <update id="increaseLikeCount">
        UPDATE posts SET like_count = like_count + 1 WHERE id = #{id}
    </update>

    <!-- 减少点赞数 -->
    <update id="decreaseLikeCount">
        UPDATE posts SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0
    </update>

    <!-- 增加评论数 -->
    <update id="increaseCommentCount">
        UPDATE posts SET comment_count = comment_count + 1 WHERE id = #{id}
    </update>

    <!-- 减少评论数 -->
    <update id="decreaseCommentCount">
        UPDATE posts SET comment_count = comment_count - 1 WHERE id = #{id} AND comment_count > 0
    </update>

    <!-- 增加收藏数 -->
    <update id="increaseCollectCount">
        UPDATE posts SET collect_count = collect_count + 1 WHERE id = #{id}
    </update>

    <!-- 减少收藏数 -->
    <update id="decreaseCollectCount">
        UPDATE posts SET collect_count = collect_count - 1 WHERE id = #{id} AND collect_count > 0
    </update>

    <!-- 设置置顶状态 -->
    <update id="updateTopStatus">
        UPDATE posts SET is_top = #{isTop}, updated_time = CURRENT_TIMESTAMP WHERE id = #{id}
    </update>

    <!-- 设置热门状态 -->
    <update id="updateHotStatus">
        UPDATE posts SET is_hot = #{isHot}, updated_time = CURRENT_TIMESTAMP WHERE id = #{id}
    </update>

    <!-- 统计帖子数量 -->
    <select id="countPosts" resultType="int">
        SELECT COUNT(*)
        FROM posts
        <where>
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%') 
                OR content LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>
