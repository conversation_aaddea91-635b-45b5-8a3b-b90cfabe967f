<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.community.forum.dao.UserDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="gender" property="gender"/>
        <result column="birthday" property="birthday"/>
        <result column="signature" property="signature"/>
        <result column="status" property="status"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_ip" property="lastLoginIp"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, nickname, avatar, gender, birthday, signature, 
        status, last_login_time, last_login_ip, created_time, updated_time
    </sql>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email}
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, password, email, nickname, avatar, gender, birthday, signature, status)
        VALUES (#{username}, #{password}, #{email}, #{nickname}, #{avatar}, #{gender}, #{birthday}, #{signature}, #{status})
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="User">
        UPDATE users
        <set>
            <if test="email != null">email = #{email},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="signature != null">signature = #{signature},</if>
            <if test="status != null">status = #{status},</if>
            updated_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        UPDATE users
        SET status = #{status}, updated_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 更新最后登录信息 -->
    <update id="updateLastLogin">
        UPDATE users
        SET last_login_time = CURRENT_TIMESTAMP, last_login_ip = #{lastLoginIp}, updated_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteById">
        DELETE FROM users WHERE id = #{id}
    </delete>

    <!-- 查询用户列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        <where>
            <if test="keyword != null and keyword != ''">
                AND (username LIKE CONCAT('%', #{keyword}, '%') 
                OR nickname LIKE CONCAT('%', #{keyword}, '%') 
                OR email LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_time DESC
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="int">
        SELECT COUNT(*)
        FROM users
        <where>
            <if test="keyword != null and keyword != ''">
                AND (username LIKE CONCAT('%', #{keyword}, '%') 
                OR nickname LIKE CONCAT('%', #{keyword}, '%') 
                OR email LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameExists" resultType="int">
        SELECT COUNT(*)
        FROM users
        WHERE username = #{username}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="checkEmailExists" resultType="int">
        SELECT COUNT(*)
        FROM users
        WHERE email = #{email}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
