<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 社区论坛</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 500;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .social-login {
            border-top: 1px solid #eee;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="login-container">
                    <div class="login-header">
                        <h3 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            社区论坛
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">欢迎回来，请登录您的账户</p>
                    </div>
                    
                    <div class="login-body">
                        <!-- 错误提示 -->
                        <div id="errorAlert" class="alert alert-danger d-none" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorMessage"></span>
                        </div>

                        <!-- 登录表单 -->
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>用户名
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>密码
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="请输入密码" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    记住我
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-login w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>登录
                            </button>
                        </form>

                        <!-- 其他选项 -->
                        <div class="text-center">
                            <p class="mb-2">
                                <a href="#" class="text-decoration-none">忘记密码？</a>
                            </p>
                            <p class="mb-0">
                                还没有账户？
                                <a href="/register" class="text-decoration-none fw-bold">立即注册</a>
                            </p>
                        </div>

                        <!-- 社交登录 -->
                        <div class="social-login">
                            <p class="text-center text-muted mb-3">或使用以下方式登录</p>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fab fa-qq me-2"></i>QQ登录
                                </button>
                                <button class="btn btn-outline-success" type="button">
                                    <i class="fab fa-weixin me-2"></i>微信登录
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 返回首页 -->
                <div class="text-center mt-3">
                    <a href="/" class="text-white text-decoration-none">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 密码显示/隐藏切换
            $('#togglePassword').click(function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // 登录表单提交
            $('#loginForm').submit(function(e) {
                e.preventDefault();
                
                const username = $('#username').val().trim();
                const password = $('#password').val();
                
                // 基础验证
                if (!username) {
                    showError('请输入用户名');
                    return;
                }
                
                if (!password) {
                    showError('请输入密码');
                    return;
                }
                
                // 提交登录请求
                $.ajax({
                    url: '/api/user/login',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        username: username,
                        password: password
                    }),
                    success: function(result) {
                        if (result.code === 200) {
                            // 登录成功，跳转到首页或之前的页面
                            const returnUrl = getUrlParameter('returnUrl') || '/';
                            window.location.href = returnUrl;
                        } else {
                            showError(result.message || '登录失败');
                        }
                    },
                    error: function() {
                        showError('网络错误，请稍后重试');
                    }
                });
            });
        });

        // 显示错误信息
        function showError(message) {
            $('#errorMessage').text(message);
            $('#errorAlert').removeClass('d-none');
            
            // 3秒后自动隐藏
            setTimeout(function() {
                $('#errorAlert').addClass('d-none');
            }, 3000);
        }

        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            const results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
    </script>
</body>
</html>
