<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 社区论坛</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 500;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .alert {
            border-radius: 10px;
        }
        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 5px;
            transition: all 0.3s;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
        .form-text.text-success { color: #28a745 !important; }
        .form-text.text-danger { color: #dc3545 !important; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-container">
                    <div class="register-header">
                        <h3 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            注册账户
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">加入我们的社区，开始精彩的交流之旅</p>
                    </div>
                    
                    <div class="register-body">
                        <!-- 错误提示 -->
                        <div id="errorAlert" class="alert alert-danger d-none" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorMessage"></span>
                        </div>

                        <!-- 成功提示 -->
                        <div id="successAlert" class="alert alert-success d-none" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <span id="successMessage"></span>
                        </div>

                        <!-- 注册表单 -->
                        <form id="registerForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>用户名 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           placeholder="3-20个字符" required>
                                    <div class="form-text" id="usernameHelp"></div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="nickname" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>昵称 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="nickname" name="nickname" 
                                           placeholder="显示名称" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>邮箱地址 <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="<EMAIL>" required>
                                <div class="form-text" id="emailHelp"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>密码 <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="至少6个字符" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength" id="passwordStrength"></div>
                                <div class="form-text" id="passwordHelp">密码强度：包含大小写字母、数字和特殊字符</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">
                                    <i class="fas fa-lock me-1"></i>确认密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" 
                                       placeholder="再次输入密码" required>
                                <div class="form-text" id="confirmPasswordHelp"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="gender" class="form-label">
                                    <i class="fas fa-venus-mars me-1"></i>性别
                                </label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="0">保密</option>
                                    <option value="1">男</option>
                                    <option value="2">女</option>
                                </select>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    我已阅读并同意 <a href="#" class="text-decoration-none">用户协议</a> 和 <a href="#" class="text-decoration-none">隐私政策</a>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-register w-100 mb-3">
                                <i class="fas fa-user-plus me-2"></i>注册账户
                            </button>
                        </form>

                        <!-- 其他选项 -->
                        <div class="text-center">
                            <p class="mb-0">
                                已有账户？
                                <a href="/login" class="text-decoration-none fw-bold">立即登录</a>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 返回首页 -->
                <div class="text-center mt-3">
                    <a href="/" class="text-white text-decoration-none">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // 密码显示/隐藏切换
            $('#togglePassword').click(function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // 用户名验证
            $('#username').on('blur', function() {
                const username = $(this).val().trim();
                if (username.length >= 3) {
                    checkUsername(username);
                }
            });

            // 邮箱验证
            $('#email').on('blur', function() {
                const email = $(this).val().trim();
                if (email && isValidEmail(email)) {
                    checkEmail(email);
                }
            });

            // 密码强度检测
            $('#password').on('input', function() {
                const password = $(this).val();
                checkPasswordStrength(password);
                checkPasswordMatch();
            });

            // 确认密码验证
            $('#confirmPassword').on('input', function() {
                checkPasswordMatch();
            });

            // 注册表单提交
            $('#registerForm').submit(function(e) {
                e.preventDefault();
                
                if (validateForm()) {
                    submitRegister();
                }
            });
        });

        // 检查用户名是否存在
        function checkUsername(username) {
            $.get('/api/user/check-username', { username: username }, function(result) {
                const helpElement = $('#usernameHelp');
                if (result.code === 200) {
                    if (result.data) {
                        helpElement.text('用户名已存在').removeClass('text-success').addClass('text-danger');
                    } else {
                        helpElement.text('用户名可用').removeClass('text-danger').addClass('text-success');
                    }
                }
            });
        }

        // 检查邮箱是否存在
        function checkEmail(email) {
            $.get('/api/user/check-email', { email: email }, function(result) {
                const helpElement = $('#emailHelp');
                if (result.code === 200) {
                    if (result.data) {
                        helpElement.text('邮箱已被注册').removeClass('text-success').addClass('text-danger');
                    } else {
                        helpElement.text('邮箱可用').removeClass('text-danger').addClass('text-success');
                    }
                }
            });
        }

        // 检查密码强度
        function checkPasswordStrength(password) {
            const strengthBar = $('#passwordStrength');
            const helpElement = $('#passwordHelp');
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthBar.removeClass('strength-weak strength-medium strength-strong');
            
            if (strength <= 2) {
                strengthBar.addClass('strength-weak');
                helpElement.text('密码强度：弱').removeClass('text-success').addClass('text-danger');
            } else if (strength <= 3) {
                strengthBar.addClass('strength-medium');
                helpElement.text('密码强度：中等').removeClass('text-danger text-success');
            } else {
                strengthBar.addClass('strength-strong');
                helpElement.text('密码强度：强').removeClass('text-danger').addClass('text-success');
            }
        }

        // 检查密码匹配
        function checkPasswordMatch() {
            const password = $('#password').val();
            const confirmPassword = $('#confirmPassword').val();
            const helpElement = $('#confirmPasswordHelp');
            
            if (confirmPassword) {
                if (password === confirmPassword) {
                    helpElement.text('密码匹配').removeClass('text-danger').addClass('text-success');
                } else {
                    helpElement.text('密码不匹配').removeClass('text-success').addClass('text-danger');
                }
            } else {
                helpElement.text('');
            }
        }

        // 验证邮箱格式
        function isValidEmail(email) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        }

        // 表单验证
        function validateForm() {
            const username = $('#username').val().trim();
            const nickname = $('#nickname').val().trim();
            const email = $('#email').val().trim();
            const password = $('#password').val();
            const confirmPassword = $('#confirmPassword').val();
            const agreeTerms = $('#agreeTerms').is(':checked');
            
            if (!username || username.length < 3 || username.length > 20) {
                showError('用户名长度必须在3-20个字符之间');
                return false;
            }
            
            if (!nickname) {
                showError('请输入昵称');
                return false;
            }
            
            if (!email || !isValidEmail(email)) {
                showError('请输入有效的邮箱地址');
                return false;
            }
            
            if (!password || password.length < 6) {
                showError('密码长度不能少于6个字符');
                return false;
            }
            
            if (password !== confirmPassword) {
                showError('两次输入的密码不一致');
                return false;
            }
            
            if (!agreeTerms) {
                showError('请阅读并同意用户协议和隐私政策');
                return false;
            }
            
            return true;
        }

        // 提交注册
        function submitRegister() {
            const formData = {
                username: $('#username').val().trim(),
                nickname: $('#nickname').val().trim(),
                email: $('#email').val().trim(),
                password: $('#password').val(),
                gender: parseInt($('#gender').val())
            };
            
            $.ajax({
                url: '/api/user/register',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(result) {
                    if (result.code === 200) {
                        showSuccess('注册成功！3秒后跳转到登录页面...');
                        setTimeout(function() {
                            window.location.href = '/login';
                        }, 3000);
                    } else {
                        showError(result.message || '注册失败');
                    }
                },
                error: function() {
                    showError('网络错误，请稍后重试');
                }
            });
        }

        // 显示错误信息
        function showError(message) {
            $('#errorMessage').text(message);
            $('#errorAlert').removeClass('d-none');
            $('#successAlert').addClass('d-none');
            
            setTimeout(function() {
                $('#errorAlert').addClass('d-none');
            }, 5000);
        }

        // 显示成功信息
        function showSuccess(message) {
            $('#successMessage').text(message);
            $('#successAlert').removeClass('d-none');
            $('#errorAlert').addClass('d-none');
        }
    </script>
</body>
</html>
