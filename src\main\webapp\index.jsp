<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区论坛</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        .post-card {
            transition: transform 0.2s;
        }
        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .category-badge {
            font-size: 0.8rem;
        }
        .stats-item {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        .sidebar {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .hot-post {
            border-left: 3px solid #dc3545;
            padding-left: 10px;
        }
        .top-post {
            border-left: 3px solid #ffc107;
            padding-left: 10px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-comments"></i> 社区论坛
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/posts">全部帖子</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/categories">分类</a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <div class="me-3">
                        <input class="form-control" type="search" placeholder="搜索帖子..." id="searchInput">
                    </div>
                    <div id="userMenu">
                        <!-- 用户菜单将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
        <div class="row">
            <!-- 左侧内容区 -->
            <div class="col-lg-8">
                <!-- 置顶帖子 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-thumbtack text-warning"></i> 置顶帖子</h5>
                    </div>
                    <div class="card-body" id="topPosts">
                        <!-- 置顶帖子列表 -->
                    </div>
                </div>

                <!-- 最新帖子 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-clock"></i> 最新帖子</h5>
                        <div class="btn-group" role="group">
                            <input type="radio" class="btn-check" name="sortOrder" id="latest" value="latest" checked>
                            <label class="btn btn-outline-primary btn-sm" for="latest">最新</label>
                            
                            <input type="radio" class="btn-check" name="sortOrder" id="hot" value="hot">
                            <label class="btn btn-outline-primary btn-sm" for="hot">热门</label>
                            
                            <input type="radio" class="btn-check" name="sortOrder" id="mostViewed" value="view">
                            <label class="btn btn-outline-primary btn-sm" for="mostViewed">浏览最多</label>
                        </div>
                    </div>
                    <div class="card-body" id="postsList">
                        <!-- 帖子列表 -->
                    </div>
                </div>

                <!-- 分页 -->
                <nav aria-label="帖子分页" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮 -->
                    </ul>
                </nav>
            </div>

            <!-- 右侧边栏 -->
            <div class="col-lg-4">
                <!-- 用户信息卡片 -->
                <div class="card mb-4" id="userCard" style="display: none;">
                    <div class="card-body text-center">
                        <img id="userAvatar" src="" alt="用户头像" class="rounded-circle mb-3" style="width: 80px; height: 80px;">
                        <h6 id="userNickname"></h6>
                        <p class="text-muted small" id="userSignature"></p>
                        <div class="row text-center">
                            <div class="col-4">
                                <strong id="userPostCount">0</strong>
                                <div class="small text-muted">帖子</div>
                            </div>
                            <div class="col-4">
                                <strong id="userCommentCount">0</strong>
                                <div class="small text-muted">评论</div>
                            </div>
                            <div class="col-4">
                                <strong id="userLikeCount">0</strong>
                                <div class="small text-muted">获赞</div>
                            </div>
                        </div>
                        <a href="/user/profile" class="btn btn-primary btn-sm mt-3">个人中心</a>
                    </div>
                </div>

                <!-- 分类列表 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list"></i> 帖子分类</h6>
                    </div>
                    <div class="card-body" id="categoriesList">
                        <!-- 分类列表 -->
                    </div>
                </div>

                <!-- 热门帖子 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-fire text-danger"></i> 热门帖子</h6>
                    </div>
                    <div class="card-body" id="hotPosts">
                        <!-- 热门帖子列表 -->
                    </div>
                </div>

                <!-- 在线统计 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 论坛统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <strong id="totalPosts">0</strong>
                                <div class="small text-muted">总帖子</div>
                            </div>
                            <div class="col-6">
                                <strong id="totalUsers">0</strong>
                                <div class="small text-muted">总用户</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 发帖按钮 -->
    <div class="position-fixed bottom-0 end-0 p-3" id="newPostBtn" style="display: none;">
        <button class="btn btn-primary btn-lg rounded-circle" onclick="location.href='/post/new'">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 页面加载完成后初始化
        $(document).ready(function() {
            loadUserInfo();
            loadCategories();
            // loadTopPosts(); // 暂时注释，等实现帖子功能后启用
            // loadPosts(1, 'latest'); // 暂时注释，等实现帖子功能后启用
            // loadHotPosts(); // 暂时注释，等实现帖子功能后启用
            // loadStats(); // 暂时注释，等实现统计功能后启用
            
            // 绑定排序按钮事件
            $('input[name="sortOrder"]').change(function() {
                loadPosts(1, $(this).val());
            });
            
            // 绑定搜索事件
            $('#searchInput').on('keypress', function(e) {
                if (e.which === 13) {
                    searchPosts();
                }
            });
        });

        // 加载用户信息
        function loadUserInfo() {
            $.get('/api/user/current', function(result) {
                if (result.code === 200) {
                    showUserMenu(result.data);
                    showUserCard(result.data);
                    $('#newPostBtn').show();
                } else {
                    showGuestMenu();
                }
            });
        }

        // 显示用户菜单
        function showUserMenu(user) {
            $('#userMenu').html(`
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <img src="${user.avatar}" alt="头像" class="user-avatar me-1">
                        ${user.nickname}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/user/profile"><i class="fas fa-user"></i> 个人中心</a></li>
                        <li><a class="dropdown-item" href="/user/posts"><i class="fas fa-edit"></i> 我的帖子</a></li>
                        <li><a class="dropdown-item" href="/user/collections"><i class="fas fa-star"></i> 我的收藏</a></li>
                        <li><a class="dropdown-item" href="/user/notifications"><i class="fas fa-bell"></i> 消息通知</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            `);
        }

        // 显示访客菜单
        function showGuestMenu() {
            $('#userMenu').html(`
                <a href="/login" class="btn btn-outline-primary me-2">登录</a>
                <a href="/register" class="btn btn-primary">注册</a>
            `);
        }

        // 显示用户卡片
        function showUserCard(user) {
            $('#userCard').show();
            $('#userAvatar').attr('src', user.avatar);
            $('#userNickname').text(user.nickname);
            $('#userSignature').text(user.signature || '这个人很懒，什么都没留下...');
        }

        // 退出登录
        function logout() {
            $.post('/api/user/logout', function(result) {
                if (result.code === 200) {
                    location.reload();
                } else {
                    alert('退出失败');
                }
            });
        }

        // 加载分类列表
        function loadCategories() {
            $.get('/api/categories/list', function(result) {
                if (result.code === 200) {
                    let html = '';
                    result.data.forEach(function(category) {
                        html += `
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <a href="/posts?category=${category.id}" class="text-decoration-none">
                                    <i class="${category.icon}"></i> ${category.name}
                                </a>
                                <span class="badge bg-secondary">${category.postCount || 0}</span>
                            </div>
                        `;
                    });
                    $('#categoriesList').html(html);
                }
            });
        }

        // 加载置顶帖子
        function loadTopPosts() {
            $.get('/api/posts/top', function(result) {
                if (result.code === 200) {
                    let html = '';
                    result.data.forEach(function(post) {
                        html += `
                            <div class="top-post mb-2">
                                <a href="/post/${post.id}" class="text-decoration-none">
                                    <strong>${post.title}</strong>
                                </a>
                            </div>
                        `;
                    });
                    $('#topPosts').html(html || '<p class="text-muted">暂无置顶帖子</p>');
                }
            });
        }

        // 加载帖子列表
        function loadPosts(page, orderBy) {
            $.get('/api/posts/list', {
                page: page,
                size: 10,
                orderBy: orderBy
            }, function(result) {
                if (result.code === 200) {
                    displayPosts(result.data.list);
                    displayPagination(result.data, page, orderBy);
                }
            });
        }

        // 显示帖子列表
        function displayPosts(posts) {
            let html = '';
            posts.forEach(function(post) {
                html += `
                    <div class="post-card border-bottom pb-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="/post/${post.id}" class="text-decoration-none text-dark">
                                        ${post.title}
                                    </a>
                                    ${post.isHot ? '<span class="badge bg-danger ms-1">热</span>' : ''}
                                </h6>
                                <p class="text-muted small mb-2">${post.summary || ''}</p>
                                <div class="d-flex align-items-center">
                                    <img src="${post.user.avatar}" alt="头像" class="user-avatar me-2">
                                    <span class="small text-muted me-3">${post.user.nickname}</span>
                                    <span class="badge category-badge bg-light text-dark me-2">${post.category.name}</span>
                                    <span class="stats-item me-2"><i class="fas fa-eye"></i> ${post.viewCount}</span>
                                    <span class="stats-item me-2"><i class="fas fa-thumbs-up"></i> ${post.likeCount}</span>
                                    <span class="stats-item me-2"><i class="fas fa-comments"></i> ${post.commentCount}</span>
                                    <span class="stats-item">${formatDate(post.createdTime)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            $('#postsList').html(html || '<p class="text-muted text-center">暂无帖子</p>');
        }

        // 加载热门帖子
        function loadHotPosts() {
            $.get('/api/posts/hot', {limit: 5}, function(result) {
                if (result.code === 200) {
                    let html = '';
                    result.data.forEach(function(post, index) {
                        html += `
                            <div class="hot-post mb-2">
                                <span class="badge bg-danger me-2">${index + 1}</span>
                                <a href="/post/${post.id}" class="text-decoration-none small">
                                    ${post.title}
                                </a>
                            </div>
                        `;
                    });
                    $('#hotPosts').html(html || '<p class="text-muted small">暂无热门帖子</p>');
                }
            });
        }

        // 加载统计信息
        function loadStats() {
            $.get('/api/stats', function(result) {
                if (result.code === 200) {
                    $('#totalPosts').text(result.data.totalPosts || 0);
                    $('#totalUsers').text(result.data.totalUsers || 0);
                }
            });
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
            
            return date.toLocaleDateString();
        }

        // 显示分页
        function displayPagination(pageInfo, currentPage, orderBy) {
            // 分页逻辑实现
        }

        // 搜索帖子
        function searchPosts() {
            const keyword = $('#searchInput').val().trim();
            if (keyword) {
                location.href = '/posts?search=' + encodeURIComponent(keyword);
            }
        }
    </script>
</body>
</html>
