package com.community.forum.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * 应用启动测试
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
    "classpath:spring/applicationContext.xml"
})
@WebAppConfiguration
public class ApplicationStartupTest {

    @Test
    public void testApplicationContextLoads() {
        // 如果Spring上下文能够成功加载，这个测试就会通过
        System.out.println("Spring应用上下文加载成功！");
    }
}
