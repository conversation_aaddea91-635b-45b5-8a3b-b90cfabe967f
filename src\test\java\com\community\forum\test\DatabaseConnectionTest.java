package com.community.forum.test;

import org.junit.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接测试
 */
public class DatabaseConnectionTest {

    private static final String URL = "*********************************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "123456";

    @Test
    public void testDatabaseConnection() {
        Connection connection = null;
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            if (connection != null && !connection.isClosed()) {
                System.out.println("数据库连接成功！");
                System.out.println("数据库URL: " + URL);
                System.out.println("连接状态: " + (connection.isClosed() ? "已关闭" : "已连接"));
            } else {
                System.out.println("数据库连接失败！");
            }
            
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL驱动未找到: " + e.getMessage());
            e.printStackTrace();
        } catch (SQLException e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
            
            // 提供详细的错误信息
            System.err.println("错误代码: " + e.getErrorCode());
            System.err.println("SQL状态: " + e.getSQLState());
            
            // 常见错误的解决建议
            if (e.getMessage().contains("Access denied")) {
                System.err.println("建议: 检查用户名和密码是否正确");
            } else if (e.getMessage().contains("Unknown database")) {
                System.err.println("建议: 检查数据库 'community_forum' 是否存在");
            } else if (e.getMessage().contains("Connection refused")) {
                System.err.println("建议: 检查MySQL服务是否启动");
            }
        } finally {
            // 关闭连接
            if (connection != null) {
                try {
                    connection.close();
                    System.out.println("数据库连接已关闭");
                } catch (SQLException e) {
                    System.err.println("关闭连接时出错: " + e.getMessage());
                }
            }
        }
    }
}
