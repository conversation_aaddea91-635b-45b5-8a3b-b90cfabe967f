<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:websocket="http://www.springframework.org/schema/websocket"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc.xsd
       http://www.springframework.org/schema/websocket
       http://www.springframework.org/schema/websocket/spring-websocket.xsd">

    <!-- 扫描Controller层组件 -->
    <context:component-scan base-package="com.community.forum.controller"/>

    <!-- 开启SpringMVC注解驱动 -->
    <mvc:annotation-driven>
        <mvc:message-converters>
            <!-- JSON消息转换器 -->
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                <property name="supportedMediaTypes">
                    <list>
                        <value>application/json;charset=UTF-8</value>
                        <value>text/html;charset=UTF-8</value>
                    </list>
                </property>
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <!-- 静态资源处理 -->
    <mvc:resources mapping="/static/**" location="/static/"/>
    <mvc:resources mapping="/images/**" location="/images/"/>
    <mvc:resources mapping="/css/**" location="/css/"/>
    <mvc:resources mapping="/js/**" location="/js/"/>
    <mvc:resources mapping="/fonts/**" location="/fonts/"/>

    <!-- 视图解析器 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/"/>
        <property name="suffix" value=".jsp"/>
        <property name="contentType" value="text/html;charset=UTF-8"/>
    </bean>

    <!-- 文件上传配置 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="10485760"/> <!-- 10MB -->
        <property name="maxInMemorySize" value="4096"/>
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>

    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <!-- 登录拦截器 -->
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <mvc:exclude-mapping path="/"/>
            <mvc:exclude-mapping path="/login"/>
            <mvc:exclude-mapping path="/register"/>
            <mvc:exclude-mapping path="/api/user/login"/>
            <mvc:exclude-mapping path="/api/user/register"/>
            <mvc:exclude-mapping path="/api/posts/list"/>
            <mvc:exclude-mapping path="/api/posts/detail/**"/>
            <mvc:exclude-mapping path="/api/categories/list"/>
            <mvc:exclude-mapping path="/static/**"/>
            <mvc:exclude-mapping path="/images/**"/>
            <mvc:exclude-mapping path="/css/**"/>
            <mvc:exclude-mapping path="/js/**"/>
            <mvc:exclude-mapping path="/fonts/**"/>
            <bean class="com.community.forum.interceptor.LoginInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <!-- WebSocket配置 -->
    <websocket:handlers>
        <websocket:mapping path="/websocket" handler="notificationWebSocketHandler"/>
        <websocket:handshake-interceptors>
            <bean class="com.community.forum.config.WebSocketInterceptor"/>
        </websocket:handshake-interceptors>
    </websocket:handlers>

    <bean id="notificationWebSocketHandler" class="com.community.forum.config.NotificationWebSocketHandler"/>

    <!-- 全局异常处理器 -->
    <bean class="com.community.forum.config.GlobalExceptionHandler"/>

</beans>
