<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="1" failures="0" name="com.community.forum.test.DatabaseConnectionTest" time="0" errors="0" skipped="0">
  <properties>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="sun.boot.library.path" value="D:\java\java8\jre\bin"/>
    <property name="java.vm.version" value="25.421-b09"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="maven.multiModuleProjectDirectory" value="D:\Community Forum"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="path.separator" value=";"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="D:\Community Forum"/>
    <property name="java.runtime.version" value="1.8.0_421-b09"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="java.endorsed.dirs" value="D:\java\java8\jre\lib\endorsed"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="os.name" value="Windows 10"/>
    <property name="classworlds.conf" value="D:\apache-maven-3.6.0\apache-maven-3.6.0\bin\..\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\java\java8\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\Anaconda3;D:\Anaconda3\Library\mingw-w64\bin;D:\Anaconda3\Library\usr\bin;D:\Anaconda3\Library\bin;D:\Anaconda3\Scripts;D:\python386\Scripts\;D:\python386\;D:\Python\Scripts\;D:\apache-maven-3.6.0\apache-maven-3.6.0\bin;D:\java\java8\bin;D:\Python\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;D:\mysql\MySQL Server 8.0\bin;%NODE_PATH%;D:\software\node.js\;D:\software\;D:\software\nodeJs\node_global\node_modules\yarn\bin;%CATALIN A_HOM E%\bin;D:\vscode\mingw64\bin;D:\Anacanda3;D:\Anacanda3\Library\mingw-w64\bin;D:\Anacanda3\Library\usr\bin;D:\Anacanda3\Library\bin;D:\Anacanda3\Scripts;D:\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;%mysql%\bin;D:\Pycharm\PyCharm 2022.3.3\bin;;C:\Users\<USER>\AppData\Roaming\npm;D:\Bandizip\;D:\cursor\resources\app\bin;."/>
    <property name="maven.conf" value="D:\apache-maven-3.6.0\apache-maven-3.6.0\bin\../conf"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.class.version" value="52.0"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="os.version" value="10.0"/>
    <property name="library.jansi.path" value="D:\apache-maven-3.6.0\apache-maven-3.6.0\bin\..\lib\jansi-native"/>
    <property name="user.home" value="C:\Users\<USER>\apache-maven-3.6.0\apache-maven-3.6.0\bin\..\boot\plexus-classworlds-2.5.2.jar"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.home" value="D:\java\java8\jre"/>
    <property name="sun.java.command" value="org.codehaus.plexus.classworlds.launcher.Launcher clean install"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="user.language" value="zh"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.version" value="1.8.0_421"/>
    <property name="java.ext.dirs" value="D:\java\java8\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.boot.class.path" value="D:\java\java8\jre\lib\resources.jar;D:\java\java8\jre\lib\rt.jar;D:\java\java8\jre\lib\jsse.jar;D:\java\java8\jre\lib\jce.jar;D:\java\java8\jre\lib\charsets.jar;D:\java\java8\jre\lib\jfr.jar;D:\java\java8\jre\classes"/>
    <property name="sun.stderr.encoding" value="ms936"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.specification.maintenance.version" value="5"/>
    <property name="maven.home" value="D:\apache-maven-3.6.0\apache-maven-3.6.0\bin\.."/>
    <property name="file.separator" value="\"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.stdout.encoding" value="ms936"/>
    <property name="sun.desktop" value="windows"/>
    <property name="sun.cpu.isalist" value="amd64"/>
  </properties>
  <testcase classname="com.community.forum.test.DatabaseConnectionTest" name="testDatabaseConnection" time="0"/>
</testsuite>